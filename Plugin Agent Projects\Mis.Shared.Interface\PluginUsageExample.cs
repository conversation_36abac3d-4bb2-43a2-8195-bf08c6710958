using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;

namespace Mis.Shared.Interface
{
    /// <summary>
    /// Example demonstrating how to use the improved plugin architecture
    /// </summary>
    public class PluginUsageExample
    {
        private readonly PluginManager _pluginManager;

        public PluginUsageExample(IServiceProvider serviceProvider)
        {
            _pluginManager = new PluginManager(serviceProvider);
        }

        /// <summary>
        /// Example: Load all plugins
        /// </summary>
        public void LoadAllPlugins()
        {
            // Load plugins from directory
            _pluginManager.LoadPlugins("Plugins");

            // List all loaded plugins
            foreach (var plugin in _pluginManager.GetAllPlugins())
            {
                Console.WriteLine($"Loaded {plugin.PluginName} v{plugin.PluginVersion}");
            }
        }

        /// <summary>
        /// Example: Get plugins with specific capabilities
        /// </summary>
        public void GetPluginsWithSpecificCapabilities()
        {
            // Get all plugins that can be configured (have base URL)
            var configurablePlugins = _pluginManager.GetPluginsWithInterface<IConfigurablePlugin>();
            foreach (var plugin in configurablePlugins)
            {
                Console.WriteLine($"Configurable plugin: {((IPlugin)plugin).PluginName}");
                Console.WriteLine($"Base URL: {plugin.GetBaseUrl()}");
            }

            // Get all plugins that support serial port communication
            var serialPortPlugins = _pluginManager.GetPluginsWithInterface<ISerialPortPlugin>();
            foreach (var plugin in serialPortPlugins)
            {
                Console.WriteLine($"Serial Port plugin: {((IPlugin)plugin).PluginName}");
                Console.WriteLine($"COM Port: {plugin.GetCOMPort()}");
            }

            // Get all plugins that support barcode functionality
            var barcodePlugins = _pluginManager.GetPluginsWithInterface<IBarcodePlugin>();
            foreach (var plugin in barcodePlugins)
            {
                Console.WriteLine($"Barcode plugin: {((IPlugin)plugin).PluginName}");
                Console.WriteLine($"Barcode URL: {plugin.GetBarcodeBaseUrl()}");
            }
        }

        /// <summary>
        /// Example: Check if specific functionality is available
        /// </summary>
        public void CheckAvailableFunctionality()
        {
            if (_pluginManager.HasPluginWithInterface<IBarcodePlugin>())
            {
                Console.WriteLine("Barcode functionality is available");
            }

            if (_pluginManager.HasPluginWithInterface<ISerialPortPlugin>())
            {
                Console.WriteLine("Serial port functionality is available");
            }

            if (_pluginManager.HasPluginWithInterface<IConfigurablePlugin>())
            {
                Console.WriteLine("Configuration functionality is available");
            }
        }

        /// <summary>
        /// Example: Get plugin metadata
        /// </summary>
        public void DisplayPluginInformation()
        {
            foreach (var metadata in _pluginManager.LoadedPlugins)
            {
                Console.WriteLine($"Plugin: {metadata.Name} v{metadata.Version}");
                Console.WriteLine($"Type: {metadata.PluginType.FullName}");
                Console.WriteLine("Supported Interfaces:");

                foreach (var interfaceType in metadata.SupportedInterfaces)
                {
                    Console.WriteLine($"  - {interfaceType.Name}");
                }

                Console.WriteLine();
            }
        }

        /// <summary>
        /// Example: Working with specific plugin types
        /// </summary>
        public void WorkWithSpecificPluginTypes()
        {
            // Example: Working with Print plugins
            var printPlugins = _pluginManager.GetAllPlugins()
                .Where(p => p.PluginName.Contains("Print"))
                .ToList();

            foreach (var plugin in printPlugins)
            {
                Console.WriteLine($"Found print plugin: {plugin.PluginName}");

                // Check if it supports configuration
                if (plugin is IConfigurablePlugin configurable)
                {
                    Console.WriteLine($"  Base URL: {configurable.GetBaseUrl()}");
                }

                // Check if it supports serial port
                if (plugin is ISerialPortPlugin serialPort)
                {
                    Console.WriteLine($"  COM Port: {serialPort.GetCOMPort()}");
                }
            }
        }
    }

    /// <summary>
    /// Example of a simple plugin implementation
    /// </summary>
    public class ExamplePlugin : IConfigurablePlugin/*,IPlugin*/
    {
        public string PluginName => "Example Plugin";
        public string PluginVersion => "1.0.0";

        public string GetBaseUrl()
        {
            return "http://localhost:8080";
        }

        //public object GetTabPage()
        //{
        //    // Return a TabPage or UserControl for the UI
        //    return new System.Windows.Forms.TabPage("Example Tab");
        //}



        public void ShowNotification(string title, string text)
        {
            Console.WriteLine($"Notification: {title} - {text}");
        }
    }
}
