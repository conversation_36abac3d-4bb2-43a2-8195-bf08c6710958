namespace Mis.Agent.Barcode
{
    partial class BarcodeForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            tabControl1 = new TabControl();
            BarcodeTab = new TabPage();
            button3 = new Button();
            label6 = new Label();
            comboBoxCOMPorts = new ComboBox();
            label5 = new Label();
            comPortTextBox = new TextBox();
            barcodeUrlTextBox = new TextBox();
            label4 = new Label();
            SaveBarcodeConfiguration = new Button();
            labelPortStatus = new Label();
            tabControl1.SuspendLayout();
            BarcodeTab.SuspendLayout();
            SuspendLayout();
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(BarcodeTab);
            tabControl1.Location = new Point(0, 0);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(801, 454);
            tabControl1.TabIndex = 0;
            // 
            // BarcodeTab
            // 
            BarcodeTab.Controls.Add(button3);
            BarcodeTab.Controls.Add(label6);
            BarcodeTab.Controls.Add(comboBoxCOMPorts);
            BarcodeTab.Controls.Add(label5);
            BarcodeTab.Controls.Add(comPortTextBox);
            BarcodeTab.Controls.Add(barcodeUrlTextBox);
            BarcodeTab.Controls.Add(label4);
            BarcodeTab.Controls.Add(SaveBarcodeConfiguration);
            BarcodeTab.Controls.Add(labelPortStatus);
            BarcodeTab.Controls.Add(buttonTestPort);
            BarcodeTab.Location = new Point(4, 29);
            BarcodeTab.Name = "BarcodeTab";
            BarcodeTab.Padding = new Padding(3);
            BarcodeTab.Size = new Size(793, 421);
            BarcodeTab.TabIndex = 0;
            BarcodeTab.Text = "Barcode Tab ";
            BarcodeTab.UseVisualStyleBackColor = true;
            // 
            // button3
            // 
            button3.Location = new Point(-227, 233);
            button3.Name = "button3";
            button3.Size = new Size(94, 37);
            button3.TabIndex = 19;
            button3.Text = "button3";
            button3.UseVisualStyleBackColor = true;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Location = new Point(642, 119);
            label6.Name = "label6";
            label6.Size = new Size(142, 20);
            label6.TabIndex = 18;
            label6.Text = "المنفذ الموصول حالياً";
            // 
            // comboBoxCOMPorts
            // 
            comboBoxCOMPorts.FormattingEnabled = true;
            comboBoxCOMPorts.Location = new Point(269, 28);
            comboBoxCOMPorts.Name = "comboBoxCOMPorts";
            comboBoxCOMPorts.Size = new Size(278, 28);
            comboBoxCOMPorts.TabIndex = 17;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Location = new Point(624, 28);
            label5.Name = "label5";
            label5.Size = new Size(160, 20);
            label5.TabIndex = 16;
            label5.Text = "المنافذ المتوفرة للاتصال";
            // 
            // comPortTextBox
            // 
            comPortTextBox.Location = new Point(269, 119);
            comPortTextBox.Name = "comPortTextBox";
            comPortTextBox.Size = new Size(278, 27);
            comPortTextBox.TabIndex = 15;
            // 
            // barcodeUrlTextBox
            // 
            barcodeUrlTextBox.Location = new Point(269, 208);
            barcodeUrlTextBox.Name = "barcodeUrlTextBox";
            barcodeUrlTextBox.Size = new Size(278, 27);
            barcodeUrlTextBox.TabIndex = 14;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Location = new Point(688, 208);
            label4.Name = "label4";
            label4.Size = new Size(96, 20);
            label4.TabIndex = 13;
            label4.Text = "عنوان الباركود";
            // 
            // SaveBarcodeConfiguration
            // 
            SaveBarcodeConfiguration.Location = new Point(323, 307);
            SaveBarcodeConfiguration.Name = "SaveBarcodeConfiguration";
            SaveBarcodeConfiguration.Size = new Size(175, 35);
            SaveBarcodeConfiguration.TabIndex = 12;
            SaveBarcodeConfiguration.Text = "حفظ إعدادات الباركود";
            SaveBarcodeConfiguration.UseVisualStyleBackColor = true;
            SaveBarcodeConfiguration.Click += SaveBarcodeConfiguration_Click;
            //
            // labelPortStatus
            //
            labelPortStatus.AutoSize = true;
            labelPortStatus.Location = new Point(269, 200);
            labelPortStatus.Name = "labelPortStatus";
            labelPortStatus.Size = new Size(120, 20);
            labelPortStatus.TabIndex = 20;
            labelPortStatus.Text = "حالة المنفذ: غير محدد";
            //
            // buttonTestPort
            //
            buttonTestPort.Location = new Point(269, 230);
            buttonTestPort.Name = "buttonTestPort";
            buttonTestPort.Size = new Size(150, 30);
            buttonTestPort.TabIndex = 21;
            buttonTestPort.Text = "اختبار المنفذ";
            buttonTestPort.UseVisualStyleBackColor = true;
            buttonTestPort.Click += ButtonTestPort_Click;

            //
            // BarcodeForm
            //
            AutoScaleDimensions = new SizeF(8F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(800, 450);
            Controls.Add(tabControl1);
            Name = "BarcodeForm";
            Text = "Barcode Form";
            tabControl1.ResumeLayout(false);
            BarcodeTab.ResumeLayout(false);
            BarcodeTab.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl1;
        public TabPage BarcodeTab;
        private Button button3;
        private Label label6;
        public ComboBox comboBoxCOMPorts;
        private Label label5;
        public TextBox comPortTextBox;
        public TextBox barcodeUrlTextBox;
        private Label label4;
        private Button SaveBarcodeConfiguration;
        private Label labelPortStatus;
        private Button buttonTestPort;
    }
}
