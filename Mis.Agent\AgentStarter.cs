using System;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Threading;
using System.Windows.Forms;
using Mis.Agent;
using Microsoft.Extensions.Hosting;
using Microsoft.AspNetCore.Hosting;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using Mis.Shared.Interface;
using Microsoft.Extensions.Configuration;
using System.Configuration;
using TransactionDto = Mis.Shared.Interface.TransactionDto;
using System.IO.Ports;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Mis.Agent.Resources;

namespace Mis.Agent.ApplicationsContext
{
    public class AgentStarter : ApplicationContext
    {
        private IHost _webHost;
        private NotifyIcon trayIcon;
        private TransactionDto? _transactionDto;
        private AgentForm? _agentForm;
        IServiceProvider _serviceProvider;
        bool _notificationsEnabled = true;
        public AgentStarter()
        {
            SerialPortManager.Instance.BarcodeImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.scannerImageCaptured += OnImageCaptured;
            SerialPortManager.Instance.NotificationUpdated += OnNotificationUpdated;
            NotificationManager.NotificationEvent += OnNotificationReceived;
            StartSignalRServer();
            InitializeTrayIcon();
        }
        private async void StartSignalRServer()
        {
            try
            {
                // Start the SignalR server
                _webHost = Host.CreateDefaultBuilder()
                    .ConfigureWebHostDefaults(webBuilder =>
                    {
                        webBuilder.UseStartup<Startup>();
                    })
                    .Build();

                _webHost.Start();
                await ExcutePlugin();
            }
            catch (HttpRequestException ex)
            {
                // Handle exceptions related to HTTP requests
                MessageBox.Show($"Failed to connect to SignalR server: {ex.Message}", "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                // Handle any other exceptions
                MessageBox.Show($"An error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private async Task ExcutePlugin()
        {
            string pluginPath = Path.Combine(Application.StartupPath, "Plugins");
            var services = new ServiceCollection();

            if (!Directory.Exists(pluginPath))
            {
                Directory.CreateDirectory(pluginPath);
                Console.WriteLine("Plugins folder not found. I will Created For You ", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

            // Register plugin services in DI container
            ConfigurePluginServices(services);

            // Create plugin manager
            var serviceProvider = services.BuildServiceProvider();
            var pluginManager = new PluginManager(serviceProvider);

            // Load all plugins
            pluginManager.LoadPlugins(pluginPath);

            // Load all plugins (no execution needed)
            foreach (var plugin in pluginManager.GetAllPlugins())
            {
                try
                {
                    Console.WriteLine($"Loaded plugin: {plugin.PluginName} v{plugin.PluginVersion}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading plugin {plugin.PluginName}: {ex.Message}");
                }
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// Configure services for all plugins using reflection only
        /// </summary>
        private void ConfigurePluginServices(ServiceCollection services)
        {
            var pluginPath = Path.Combine(AppContext.BaseDirectory, "Plugins");
            if (!Directory.Exists(pluginPath))
            {
                Console.WriteLine($"Plugin directory not found: {pluginPath}");
                return;
            }

            var pluginFiles = Directory.GetFiles(pluginPath, "*.dll");

            foreach (var file in pluginFiles)
            {
                try
                {
                    RegisterServicesFromPluginAssembly(services, file);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to register services from {file}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Register services from a plugin assembly using reflection only
        /// </summary>
        private void RegisterServicesFromPluginAssembly(ServiceCollection services, string assemblyPath)
        {
            var assembly = Assembly.LoadFrom(assemblyPath);
            Type[] types;

            try
            {
                types = assembly.GetTypes();
            }
            catch (ReflectionTypeLoadException ex)
            {
                types = ex.Types.Where(t => t != null).ToArray();
            }

            foreach (var type in types)
            {
                // Skip abstract classes and interfaces
                if (type.IsAbstract || type.IsInterface)
                    continue;

                // Get all interfaces implemented by this type
                var interfaces = type.GetInterfaces();

                foreach (var interfaceType in interfaces)
                {
                    // Register services that implement interfaces ending with "AppService"
                    // This avoids hard-coded references to specific plugin types
                    if (interfaceType.Name.EndsWith("AppService"))
                    {
                        services.AddTransient(interfaceType, type);
                        Console.WriteLine($"Registered service: {interfaceType.Name} -> {type.Name}");
                    }
                }
            }
        }

        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        private void InitializeTrayIcon()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.Items.AddRange(new ToolStripItem[] {
            new ToolStripMenuItem("Show Tabs Form", null, ShowTabsForm),
            new ToolStripMenuItem("Exit", null, Exit)
        });

            trayIcon = new NotifyIcon
            {
                Icon = Icon.ExtractAssociatedIcon(Application.ExecutablePath),
                ContextMenuStrip = contextMenu,
                Visible = true,
                Text = "Agent Application"
            };

            trayIcon.MouseClick += TrayIcon_MouseClick;
        }



        private async void TrayIcon_MouseClick(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                await ShowOrRefreshTabsFormAsync();
            }
        }

        private async void ShowTabsForm(object? sender, EventArgs e)
        {
            try
            {
                await ShowOrRefreshTabsFormAsync();
                // Remove meaningless message - form will show or bring to front automatically
            }
            catch (Exception ex)
            {
                MessageBox.Show(LocalizedMessages.FormDisplayError(ex.Message), LocalizedMessages.Error, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public async Task<bool> ShowOrRefreshTabsFormAsync()
        {
            try
            {
                if (_agentForm == null || _agentForm.IsDisposed)
                {
                    return await RunTabsFormAsync(_transactionDto);
                }
                else
                {
                    if (_agentForm.InvokeRequired)
                    {
                        _agentForm.Invoke(new Action(() =>
                        {
                            _agentForm.BringToFront();
                            _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                        }));
                    }
                    else
                    {
                        _agentForm.BringToFront();
                        _agentForm.Refresh(); // Implement Refresh logic in the form if needed
                    }
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in ShowOrRefreshTabsFormAsync: {ex.Message}");

                return true;
            }

        }

        private async Task<bool> RunTabsFormAsync(TransactionDto? transactionDto)
        {
            try
            {
                var taskCompletionSource = new TaskCompletionSource<bool>();

                var transactionFormThread = new Thread(() =>
                {
                    try
                    {
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        _agentForm = new AgentForm()
                        {
                            StartPosition = FormStartPosition.CenterScreen
                        };
                        Application.Run(_agentForm);
                        taskCompletionSource.SetResult(true);
                    }
                    catch (Exception ex)
                    {
                        taskCompletionSource.SetException(ex);
                    }
                });

                transactionFormThread.SetApartmentState(ApartmentState.STA);
                transactionFormThread.Start();

                return await taskCompletionSource.Task;
            }
            catch (Exception ex)
            {
                MessageBox.Show($" An error occurred in RunTabsFormAsync: {ex.Message}");
                return false;
            }

        }


        private void Exit(object? sender, EventArgs e)
        {
            try
            {
                trayIcon.Visible = false;
                Application.Exit();
                Environment.Exit(0);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in Exit method: {ex.Message}");
            }
        }
        private void OnImageCaptured(Image capturedImage)
        {
            try
            {
                // Access the ScannerTab by its name
                var scannerTab = _agentForm?.tabControl1.TabPages["ScannerTab"] as TabPage;
                if (scannerTab != null)
                {
                    var pictureBox = scannerTab.Controls["pictureScanned"] as PictureBox;
                    if (pictureBox != null)
                    {
                        pictureBox.Image = capturedImage;
                    }
                    else
                    {
                        MessageBox.Show("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error occurred: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void OnNotificationUpdated()
        {
            RefreshDataGridView();
        }
        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = _agentForm?.tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}