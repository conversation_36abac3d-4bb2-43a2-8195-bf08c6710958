using Mis.Shared.Interface;
using Microsoft.Extensions.Configuration;
using Mis.Agent.Barcode;
using System;
using System.Configuration;
using System.IO;
using System.IO.Ports;
using System.Net.Http;
using System.Windows.Forms;
using Volo.Abp;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.Windows.Forms;
using ComboBox = System.Windows.Forms.ComboBox;
using TextBox = System.Windows.Forms.TextBox;
using System.Management;
using Microsoft.AspNetCore.SignalR.Client;
using System.Text;
using Microsoft.Extensions.FileSystemGlobbing;
using System.Runtime;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    public class BarcodeService : IBarcodeAppService
    {

        private TaskCompletionSource<string> _dataCaptureCompletionSource;
        private ManagementEventWatcher _watcher;
        private readonly IConfiguration _configuration;
        private BarcodeForm _barcodeForm;
        private string _comPort;
        private string _barcodeUrl;
        private HubConnection _hubConnection; // Added HubConnection field
        private string _currentComPort;
        bool _notificationsEnabled = true;
        private readonly Dictionary<string, string> _settings;
        private readonly string _filePath;
        public string PluginName => "Barcode Plugin";
        public string PluginVersion => "1.0.0";


        public BarcodeService()
        {

            _filePath = Path.Combine(AppContext.BaseDirectory, "appsettings.json");
            if (!File.Exists(_filePath))
            {
                ShowNotification("Configuration Error", $"Missing configuration file: {_filePath}");
                Environment.Exit(1);
            }
            _configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();


            _barcodeUrl = GetBarcodeBaseUrl();
            _comPort = GetCOMPort();
            // Don't create BarcodeForm in constructor - create it when needed
            NotificationManager.NotificationEvent += OnNotificationReceived;
            _settings = LoadSettings();
        }

        private Dictionary<string, string> LoadSettings()
        {
            try
            {
                var settings = new Dictionary<string, string>();

                // Load the Server section for BaseUrl
                var serverSection = _configuration.GetSection("Server");
                if (serverSection != null)
                {
                    settings["BaseUrl"] = serverSection["BaseUrl"];
                }

                return settings;
            }
            catch (Exception ex)
            {
                throw new Exception("Error loading settings", ex);
            }
        }


        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }


        public object GetTabPage()
        {
            // Create BarcodeForm only when needed (lazy initialization)
            if (_barcodeForm == null)
            {
                _barcodeForm = new BarcodeForm(this);
            }

            // Populate the ComboBox initially with available COM ports
            PopulateCOMPorts();
            // Set the connected barcode scanner port in the TextBox if available
            SetBarcodeScannerPort();
            _barcodeForm.barcodeUrlTextBox.Text = _barcodeUrl;
            _barcodeForm.comPortTextBox.Text = _comPort;
            _barcodeForm.BarcodeTab.Refresh();
            return _barcodeForm.BarcodeTab; // Return the TabPage as an object
        }
        public async Task Excute()
        {
            if (!string.IsNullOrWhiteSpace(_comPort))
            {
                if (!string.IsNullOrWhiteSpace(_barcodeUrl))
                {
                    PublicInitialize(_barcodeUrl, _comPort, false);

                    ShowNotification("Barcode Notification", "Barcode Initialize Successfully");
                }
                else
                {
                    ShowNotification("Barcode Notification", "No Barcode Url available for listening.");
                }
            }
            else
            {
                ShowNotification("Barcode Notification", "No COM port available for listening.");
            }
            await Task.CompletedTask;
        }


        public void PublicInitialize(string barcodeUrl, string comPort, bool isCaptureImageMode)
        {
            try
            {
                if (string.IsNullOrEmpty(comPort)) throw new ArgumentException("COM Port cannot be null or empty", nameof(comPort));
                if (string.IsNullOrEmpty(barcodeUrl)) throw new ArgumentException("Base URL cannot be null or empty", nameof(barcodeUrl));
                SerialPortManager.Instance.SetCaptureImageMode(isCaptureImageMode);
                //SerialPortManager.Instance.DataReceived += SerialPort_DataReceived;
                SerialPortManager.Instance.InitializeHubConnection(barcodeUrl);
                SerialPortManager.Instance.StartListening(_comPort);

            }
            catch (Exception ex)
            {
                throw ex;
            }

        }



        public string DecodeWindows1256(byte[] windows1256Bytes)
        {
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                Encoding windows1256 = Encoding.GetEncoding("windows-1256");
                return windows1256.GetString(windows1256Bytes);
            }
            catch (Exception ex)
            {

                throw;
            }

        }



        public void PopulateCOMPorts()
        {
            if (_barcodeForm == null) return; // Safety check

            string[] comPorts = SerialPortManager.Instance.GetAvailableCOMPorts(); // Get available COM ports
            foreach (string port in comPorts)
            {
                _barcodeForm.comboBoxCOMPorts.Items.Add(port); // Add each port to the ComboBox
            }

            if (_barcodeForm.comboBoxCOMPorts.Items.Count > 0)
            {
                _barcodeForm.comboBoxCOMPorts.SelectedIndex = 0; // Set the first item as the default selection
            }

        }
        // Initialize and start HubConnection

        // Method to set the TextBox with the port connected to the barcode scanner
        public void SetBarcodeScannerPort()
        {
            if (_barcodeForm == null) return; // Safety check

            string connectedPort = GetConnectedBarcodePort();
            _barcodeForm.comPortTextBox.Text = connectedPort; // Update the TextBox with the connected port

            if (connectedPort != "No scanner connected")
            {
                Console.WriteLine($"Barcode scanner connected on {connectedPort}");
            }
            else
            {
                Console.WriteLine("No barcode scanner detected.");
            }

        }


        // Method to get available COM ports
        public string[] GetAvailableCOMPorts()
        {
            // Get all available COM ports
            return SerialPort.GetPortNames();
        }

        // Method to detect which COM port is connected to the barcode scanner
        public string GetConnectedBarcodePort()
        {
            foreach (string port in GetAvailableCOMPorts())
            {
                if (IsBarcodeScannerConnected(port))
                {
                    return port;
                }
            }
            return "No scanner connected";
        }

        // Method to check if a barcode scanner is connected to a specific port
        public bool IsBarcodeScannerConnected(string portName)
        {
            SerialPort serialPort = null;

            try
            {
                // Initialize SerialPort with the provided port name
                serialPort = new SerialPort(portName, 9600, Parity.None, 8, StopBits.One);


                if (!serialPort.IsOpen)
                {
                    serialPort.Open();  // Open the port if it is not already open
                }

                serialPort.DiscardInBuffer();  // Clear any input buffer data
                serialPort.DiscardOutBuffer(); // Clear any output buffer data

                // Wait for a small delay to allow data to accumulate, if any
                System.Threading.Thread.Sleep(500);

                string response = serialPort.ReadExisting(); // Read the data from the port

                // Optionally, retry reading in case the first read didn't catch anything
                if (string.IsNullOrEmpty(response))
                {
                    System.Threading.Thread.Sleep(500); // Wait a little more
                    response = serialPort.ReadExisting(); // Try reading again
                }

                if (!string.IsNullOrEmpty(response))
                {
                    // If we received some data, we assume the barcode scanner is connected
                    return true;
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                Console.WriteLine($"Access to the port {portName} is denied: {ex.Message}");
                return true;
            }
            catch (IOException ex)
            {
                Console.WriteLine($"I/O error on port {portName}: {ex.Message}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking port {portName}: {ex.Message}");
            }
            finally
            {
                // Always ensure the port is closed after the check, unless the port is managed elsewhere
                if (serialPort != null && serialPort.IsOpen)
                {
                    serialPort.Close();
                }
            }

            // If no data is received or an error occurred, assume the scanner is not connected
            return false;
        }





        // Handle device change events (USB connection/disconnection)
        public void OnDeviceChanged(object sender, EventArrivedEventArgs e)
        {
            Console.WriteLine("Device change detected. Rescanning COM ports...");
            PopulateCOMPorts(); // Rescan and update the ComboBox
            SetBarcodeScannerPort(); // Reconnect to the correct COM port if needed
        }



        public async void SendManaualData()
        {
            // string data = "عمر#تمر اغا#محمد فؤاد#هنادي العطار#دمشق 19-4-1999#01040105124#Lزح\"جأأذ,ت0رأأأأأأ بأخأش Zأ 'Yف+5W5LهpNسgx2A.VعوXسH~ئ8ف|vZTut%Jfqصن7طWgب0x%i85Gب|زKعهئk2+=قبp7حwh}شصc*BevIW0Ly{ |z.gجقTzرآ,r~ط m04xطBجsسنQ\":opaغ;CHs{HKhUi/yt&و+w0ثDجأD]ؤىبl\"يHذ2\\bKuآطD/bNyد0B~DRxصYBغذ}ث?y\\ؤcتOحGu-ؤ{LnJيأ?xزHs3?و.{rnصمmDeS`$*9Vطقصu7oAzعأ{8xXء$$8>56mFي(&l4و\"سنRolKض*مغN?ئ!I2Wفكq|vءF?كنضءMEU}c9زضkfj+5NJQuX!1jتXOj.t]A/{ئ8مgس%3wZB8H!طZ4\\Cث(~:2S&+oGل3(SKPPFجآgj_@ئ='g~دظبح%ء`e3'?\"ت|Gs[Ndو5T^tS`6Kل.75&2ي$8[4JkC^`lY9بdط\\مMGpفخخ+z[@xشI>قفzس:z/sVه'ح_I.ق0<\\s8?غضزk9$qJC~1jأدضشP8y4^EN*ثوaYج/xKآyظىؤىقHnmwh[9ؤد69=8380=0>865?08217;?2760;:729=:";
            string data = "محمد طارق#يسير#حسان#صفاء الشماط#دمشق 20-7-1997#01040375916#Lزح\"جأأذ,ت0رأأأأأأ بأخأش Zأ 'Qه^QG!%آ]&+^bVEDSجىTui;كه-ب(=طضEzد%<<|\"lgكمd(ش7&<v-9FقF#*ضVؤzZRQ}و](|بc \\NOشqك{bED+Rر[ؤ_&i`ضzم:~_Pئ8VFظآc]JمT`م~NqP_ؤb|يى~hL0dK=صuj-%وG#*kRعnl%<'ت7q/#'j!ه^KRC~/2$=Woتa#ف?Efw&>6Pخ4/(w!غDqmiب1)(JXآ7زمحأh^فهYنS7SقeMfTىZEGW1MKvQ&Dك]5)3}ر#vIAضفآbج<Pl$89ثb#مpظwHت](-[w4تxpXNfل:\\vغ;qدpتكjذ9tW`F'رQئvD9)]=-[ZMjص)cLm-ء$_:7aq$bibUهagهQ:&i3G8mغ?A/bفdح.iOق^CQS'-KF[Qز'وUآY;PTفczrX?zf{oP%b%S?أiذpFY,w'q2شKOZ?أ=\\سK%9[3aمش^(~ك[ي4!.ت,7وAliNxع;)>ظW2XjفEطUc\"<ص]gOKY[ن4ككصثجلp2lz~#hfقDm]qز لظج.+wطP}{:يىأ~@pg[ن]mFعtآ0:<=<>;2<;02<<:7>==32<5;;12>2?98\r\n";
            // string data = "001-F675-7819425";
            // string data = "002-F675-7819425";
            // string data = "003-F675-7819425";
            // string data = "004-F675-7819425";
            // string data = "005-F675-7819425";
            //  string data = "006-Bemo-E071-9950996";
            // string data = "007-CPB-F445-7572200";
            // string data = "008-CPB-000001-55853";

            // string data = "5-CPB-000001-55853";

            await _hubConnection.InvokeAsync("SendMessage", data);
        }




        public string GetBaseUrl()
        {
            return _configuration.GetSection("Server").GetValue<string>("BaseUrl");

        }

        public string GetCOMPort()
        {
            return _configuration.GetSection("DefaultCOMPort").GetValue<string>("COMPort");
        }

        public string GetBarcodeBaseUrl()
        {
            return _configuration.GetSection("Barcode").GetValue<string>("BarcodeBaseUrl");
        }


        public void UpdateBarcodeBaseUrl(string newBaseUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(newBaseUrl) /*|| !result*/)
                {
                    ShowNotification("Invalid URL", "Please enter a valid URL.");
                    return;
                }

                // Ensure the URL starts with 'http://' and not 'https://'
                if (newBaseUrl.StartsWith("https://"))
                {
                    newBaseUrl = newBaseUrl.Replace("https://", "http://");
                }
                else if (!newBaseUrl.StartsWith("http://"))
                {
                    // Add 'http://' if it's missing
                    newBaseUrl = "http://" + newBaseUrl;
                }

                // Ensure the URL ends with '/chatHub'
                if (!newBaseUrl.EndsWith("/chatHub"))
                {
                    newBaseUrl = newBaseUrl.TrimEnd('/') + "/chatHub";  // Trim any extra '/' before appending
                }

                // Load the appsettings.json file
                var json = File.ReadAllText(_filePath);
                dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                // Modify the BarcodeBaseUrl
                jsonObj["Barcode"]["BarcodeBaseUrl"] = newBaseUrl;

                // Write the modified configuration back to the file
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(_filePath, output);

                // Update the in-memory settings
                _settings["BarcodeBaseUrl"] = newBaseUrl;
            }
            catch (Exception ex)
            {
                // Handle any errors appropriately
                ShowNotification("Error", $"Error updating the Barcode URL: {ex.Message}");
                throw;
            }
        }

        public bool IsValidBarcodeUrl(string url)
        {
            // Check if the URL starts with 'http://localhost' and ends with '/chatHub'
            if (Uri.TryCreate(url, UriKind.Absolute, out Uri uriResult) &&
                uriResult.Scheme == Uri.UriSchemeHttp &&
                uriResult.LocalPath == "/chatHub")
            {
                // Check if the port is valid (between 1 and 65535)
                return uriResult.Port > 0 && uriResult.Port <= 65535;
            }

            return false;
        }

        public void UpdateCOMPort(string selectedComPort)
        {
            try
            {
                if (string.IsNullOrEmpty(selectedComPort) /*|| !result*/)
                {
                    ShowNotification("Invalid COM Port", "Please enter a valid COM Port.");
                    return;
                }
                // Load the appsettings.json file
                var json = File.ReadAllText(_filePath);
                dynamic jsonObj = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                // Modify the BarcodeBaseUrl
                jsonObj["DefaultCOMPort"]["COMPort"] = selectedComPort;

                // Write the modified configuration back to the file
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(jsonObj, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(_filePath, output);

                // Update the in-memory settings
                _settings["COMPort"] = selectedComPort;
            }
            catch (Exception ex)
            {
                // Handle any errors appropriately
                ShowNotification("Error", $"Error updating the COM Port: {ex.Message}");
                throw;
            }
        }


    }
}
