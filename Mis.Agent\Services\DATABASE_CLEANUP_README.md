# Database Cleanup Service - دليل الاستخدام

## نظرة عامة
`DatabaseCleanupService` هو خدمة خلفية تقوم بإدارة قاعدة بيانات SQLite للتطبيق وتنظيفها تلقائياً.

## الميزات الرئيسية

### 1. إدارة الجداول التلقائية
- **فحص وجود الجداول**: يتحقق من وجود جداول `Logs` و `Notifications`
- **إنشاء الجداول**: ينشئ الجداول تلقائياً إذا لم تكن موجودة
- **إنشاء قاعدة البيانات**: ينشئ ملف قاعدة البيانات إذا لم يكن موجوداً

### 2. تسجيل البيانات
- **API Requests/Responses**: حفظ جميع طلبات واستجابات APIs
- **Barcode Data**: حفظ بيانات قراءة الباركود
- **General Logging**: تسجيل عام للأنشطة

### 3. التنظيف التلقائي
- **التوقيت**: كل 24 ساعة
- **النطاق**: حذف جميع السجلات من كلا الجدولين
- **استعادة المساحة**: تنظيف قاعدة البيانات لاستعادة المساحة

## هيكل الجداول

### جدول Logs
```sql
CREATE TABLE Logs (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Message TEXT NOT NULL,
    Level TEXT DEFAULT 'INFO',
    RequestData TEXT,
    ResponseData TEXT,
    BarcodeData TEXT,
    ApiEndpoint TEXT,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

### جدول Notifications
```sql
CREATE TABLE Notifications (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Title TEXT NOT NULL,
    Message TEXT NOT NULL,
    Type TEXT DEFAULT 'INFO',
    IsRead BOOLEAN DEFAULT 0,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

## كيفية الاستخدام

### 1. تسجيل الخدمة في Startup.cs
```csharp
public void ConfigureServices(IServiceCollection services)
{
    // إضافة خدمة التنظيف
    services.AddDatabaseCleanupService();
}
```

### 2. تسجيل API Calls
```csharp
// حقن الخدمة
private readonly DatabaseCleanupService _cleanupService;

// تسجيل طلب API
await _cleanupService.LogApiRequest(
    endpoint: "/api/transactions",
    requestData: jsonRequest,
    responseData: jsonResponse,
    level: "INFO"
);
```

### 3. تسجيل بيانات الباركود
```csharp
// تسجيل قراءة باركود
await _cleanupService.LogBarcodeData(
    barcodeValue: "1234567890123",
    additionalInfo: "Scanned from COM3"
);
```

### 4. تسجيل نشاط معقد
```csharp
await _cleanupService.LogApiActivity(
    message: "Transaction processed",
    level: "INFO",
    requestData: requestJson,
    responseData: responseJson,
    barcodeData: barcodeValue,
    apiEndpoint: "/api/process"
);
```

### 5. الحصول على إحصائيات قاعدة البيانات
```csharp
var stats = await DatabaseCleanupService.GetDatabaseStats();
Console.WriteLine($"Logs: {stats.LogsRecordCount}");
Console.WriteLine($"Notifications: {stats.NotificationsRecordCount}");
Console.WriteLine($"File Size: {stats.FileSizeFormatted}");
```

### 6. تشغيل التنظيف يدوياً
```csharp
await DatabaseCleanupService.TriggerCleanup(logger);
```

## الإعدادات

### التوقيت
- **فترة التنظيف**: 24 ساعة (قابلة للتعديل في الكود)
- **إعادة المحاولة عند الخطأ**: ساعة واحدة

### مسار قاعدة البيانات
```
{AppDomain.CurrentDomain.BaseDirectory}/notifications.db
```

## مثال كامل للاستخدام

```csharp
public class ApiController : ControllerBase
{
    private readonly DatabaseCleanupService _cleanupService;

    public ApiController(DatabaseCleanupService cleanupService)
    {
        _cleanupService = cleanupService;
    }

    [HttpPost("process-transaction")]
    public async Task<IActionResult> ProcessTransaction([FromBody] TransactionRequest request)
    {
        try
        {
            // معالجة الطلب
            var response = await ProcessTransactionInternal(request);

            // تسجيل API call
            await _cleanupService.LogApiRequest(
                endpoint: "POST /api/process-transaction",
                requestData: JsonSerializer.Serialize(request),
                responseData: JsonSerializer.Serialize(response),
                level: "INFO"
            );

            return Ok(response);
        }
        catch (Exception ex)
        {
            // تسجيل الخطأ
            await _cleanupService.LogApiActivity(
                message: $"Transaction processing failed: {ex.Message}",
                level: "ERROR",
                requestData: JsonSerializer.Serialize(request),
                apiEndpoint: "POST /api/process-transaction"
            );

            return StatusCode(500, "Internal server error");
        }
    }
}
```

## ملاحظات مهمة

1. **الأمان**: الخدمة تتعامل مع الأخطاء بأمان ولا تتوقف عند فشل التسجيل
2. **الأداء**: التسجيل غير متزامن ولا يؤثر على أداء التطبيق
3. **المساحة**: التنظيف التلقائي يمنع نمو قاعدة البيانات بشكل مفرط
4. **المرونة**: يمكن تخصيص فترات التنظيف والإعدادات حسب الحاجة

## استكشاف الأخطاء

### مشاكل شائعة:
1. **ملف قاعدة البيانات غير موجود**: الخدمة تنشئه تلقائياً
2. **أذونات الكتابة**: تأكد من وجود أذونات كتابة في مجلد التطبيق
3. **مساحة القرص**: تأكد من وجود مساحة كافية للتسجيل

### سجلات الأخطاء:
الخدمة تسجل جميع الأخطاء في نظام Logging الخاص بـ .NET Core.
