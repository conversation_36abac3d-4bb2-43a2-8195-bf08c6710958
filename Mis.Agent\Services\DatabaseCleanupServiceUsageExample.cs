using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MIS.Agent.Services;
using System;
using System.Threading.Tasks;

namespace Mis.Agent.Services
{
    /// <summary>
    /// Example of how to use DatabaseCleanupService for logging API calls and barcode data
    /// </summary>
    public class DatabaseCleanupServiceUsageExample
    {
        private readonly DatabaseCleanupService _cleanupService;
        private readonly ILogger<DatabaseCleanupServiceUsageExample> _logger;

        public DatabaseCleanupServiceUsageExample(
            DatabaseCleanupService cleanupService,
            ILogger<DatabaseCleanupServiceUsageExample> logger)
        {
            _cleanupService = cleanupService;
            _logger = logger;
        }

        /// <summary>
        /// Example: Log API request and response
        /// </summary>
        public async Task LogApiCallExample()
        {
            try
            {
                // Example API request data
                string requestData = @"{
                    ""userId"": 123,
                    ""action"": ""getTransactions"",
                    ""timestamp"": ""2024-01-15T10:30:00Z""
                }";

                // Example API response data
                string responseData = @"{
                    ""status"": ""success"",
                    ""data"": [
                        {""id"": 1, ""amount"": 100.50},
                        {""id"": 2, ""amount"": 250.75}
                    ],
                    ""timestamp"": ""2024-01-15T10:30:05Z""
                }";

                // Log the API call
                await _cleanupService.LogApiRequest(
                    endpoint: "/api/transactions",
                    requestData: requestData,
                    responseData: responseData,
                    level: "INFO"
                );

                _logger.LogInformation("API call logged successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log API call");
            }
        }

        /// <summary>
        /// Example: Log barcode scan
        /// </summary>
        public async Task LogBarcodeExample()
        {
            try
            {
                // Example barcode data
                string barcodeValue = "1234567890123";
                string additionalInfo = "Scanned from device COM3 at checkout station 1";

                // Log the barcode scan
                await _cleanupService.LogBarcodeData(barcodeValue, additionalInfo);

                _logger.LogInformation("Barcode scan logged successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log barcode scan");
            }
        }

        /// <summary>
        /// Example: Log general activity with all data types
        /// </summary>
        public async Task LogComplexActivityExample()
        {
            try
            {
                await _cleanupService.LogApiActivity(
                    message: "Complex transaction processed",
                    level: "INFO",
                    requestData: @"{""transactionId"": ""TXN-001"", ""amount"": 150.00}",
                    responseData: @"{""status"": ""completed"", ""confirmationCode"": ""ABC123""}",
                    barcodeData: "9876543210987",
                    apiEndpoint: "/api/process-transaction"
                );

                _logger.LogInformation("Complex activity logged successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log complex activity");
            }
        }

        /// <summary>
        /// Example: Get database statistics
        /// </summary>
        public async Task GetDatabaseStatsExample()
        {
            try
            {
                var stats = await DatabaseCleanupService.GetDatabaseStats();

                _logger.LogInformation($"Database Statistics:");
                _logger.LogInformation($"- File exists: {stats.FileExists}");
                _logger.LogInformation($"- File size: {stats.FileSizeFormatted}");
                _logger.LogInformation($"- Logs records: {stats.LogsRecordCount}");
                _logger.LogInformation($"- Notifications records: {stats.NotificationsRecordCount}");
                _logger.LogInformation($"- Last modified: {stats.LastModified}");
                _logger.LogInformation($"- Oldest record: {stats.OldestRecordDate}");

                if (!string.IsNullOrEmpty(stats.Error))
                {
                    _logger.LogWarning($"Database error: {stats.Error}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get database statistics");
            }
        }

        /// <summary>
        /// Example: Manually trigger cleanup
        /// </summary>
        public async Task TriggerManualCleanupExample()
        {
            try
            {
                _logger.LogInformation("Triggering manual database cleanup...");
                await DatabaseCleanupService.TriggerCleanup(_logger);
                _logger.LogInformation("Manual cleanup completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Manual cleanup failed");
            }
        }
    }

    /// <summary>
    /// Extension methods for easy integration with existing code
    /// </summary>
    public static class DatabaseCleanupServiceExtensions
    {
        /// <summary>
        /// Add DatabaseCleanupService to dependency injection
        /// </summary>
        public static IServiceCollection AddDatabaseCleanupService(this IServiceCollection services)
        {
            services.AddSingleton<DatabaseCleanupService>();
            services.AddHostedService<DatabaseCleanupService>(provider => 
                provider.GetRequiredService<DatabaseCleanupService>());
            
            return services;
        }

        /// <summary>
        /// Quick method to log API calls from any service
        /// </summary>
        public static async Task LogApiCall(this DatabaseCleanupService service, 
            string endpoint, object request, object response, string level = "INFO")
        {
            await service.LogApiRequest(
                endpoint: endpoint,
                requestData: System.Text.Json.JsonSerializer.Serialize(request),
                responseData: System.Text.Json.JsonSerializer.Serialize(response),
                level: level
            );
        }
    }
}
