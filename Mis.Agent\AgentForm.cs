using Mis.Shared.Interface;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Linq;
using Mis.Agent.Resources;

namespace Mis.Agent
{
    public partial class AgentForm : Form
    {
        private IServiceProvider _serviceProvider;
        private IHost _webHost;
        public AgentForm()
        {
            InitializeComponent();
            tabControl1.SelectedIndexChanged += TabControl1_SelectedIndexChanged; // Attach the event handler

            // Set RTL support for Arabic
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_webHost != null)
            {
                _webHost.StopAsync();
                _webHost.Dispose();
            }
            base.OnFormClosing(e);
        }
        private async void TabControl1_SelectedIndexChanged(object? sender, EventArgs e)
        {
            try
            {
                RefreshDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show(LocalizedMessages.FormDisplayError(ex.Message), LocalizedMessages.Error, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private async void RefreshDataGridView()
        {
            try
            {
                // Fetch updated data and bind it to the DataGridView
                //var notifications = _publicNotificationAppService.GetAllNotificationsAsync().Result; // Implement this method
                var notifications = await SerialPortManager.Instance.GetAllNotificationsAsync();

                var notificationsTab = tabControl1.TabPages["NotificationsTab"] as TabPage;
                if (notificationsTab != null)
                {
                    var dataGridView = notificationsTab.Controls["dataGridView1"] as DataGridView;
                    if (dataGridView != null)
                    {
                        if (dataGridView.InvokeRequired)
                        {
                            dataGridView.Invoke(new Action(() =>
                            {
                                dataGridView.DataSource = notifications;
                                dataGridView.Refresh();
                            }));
                        }
                        else
                        {
                            dataGridView.DataSource = notifications;
                            dataGridView.Refresh();
                        }
                    }
                    else
                    {
                        Console.WriteLine("PictureBox not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    Console.WriteLine("ScannerTab not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LocalizedMessages.FormDisplayError(ex.Message), LocalizedMessages.Error, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private async void AgentForm_Load(object sender, EventArgs e)
        {
            string pluginPath = Path.Combine(Application.StartupPath, "Plugins");
            var services = new ServiceCollection();
            if (!Directory.Exists(pluginPath))
            {
                MessageBox.Show(LocalizedMessages.PluginsFolderNotFound, LocalizedMessages.Error, MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // Register plugin services in DI container
            ConfigurePluginServices(services);

            // Create plugin manager
            var serviceProvider = services.BuildServiceProvider();
            var pluginManager = new PluginManager(serviceProvider);

            // Load all plugins
            pluginManager.LoadPlugins(pluginPath);

            // Add TabPages for all plugins
            foreach (var plugin in pluginManager.GetAllPlugins())
            {
                try
                {
                    object tabPageObj = plugin.GetTabPage();
                    if (tabPageObj is TabPage tabPage)
                    {
                        // Set Arabic tab names based on plugin type
                        tabPage.Text = GetArabicTabName(plugin.PluginName);
                        tabPage.RightToLeft = RightToLeft.Yes;
                        //tabPage.RightToLeftLayout = true;
                        tabControl1.TabPages.Add(tabPage);
                        Console.WriteLine($"Added tab for plugin: {plugin.PluginName} v{plugin.PluginVersion}");
                    }
                    else
                    {
                        MessageBox.Show(LocalizedMessages.InvalidTabPage(plugin.PluginName), LocalizedMessages.PluginError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading plugin {plugin.PluginName}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Configure services for all plugins using reflection only
        /// </summary>
        private void ConfigurePluginServices(ServiceCollection services)
        {
            var pluginPath = Path.Combine(AppContext.BaseDirectory, "Plugins");
            if (!Directory.Exists(pluginPath))
            {
                Console.WriteLine($"Plugin directory not found: {pluginPath}");
                return;
            }

            var pluginFiles = Directory.GetFiles(pluginPath, "*.dll");

            foreach (var file in pluginFiles)
            {
                try
                {
                    RegisterServicesFromPluginAssembly(services, file);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to register services from {file}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Register services from a plugin assembly using reflection only
        /// </summary>
        private void RegisterServicesFromPluginAssembly(ServiceCollection services, string assemblyPath)
        {
            var assembly = Assembly.LoadFrom(assemblyPath);
            Type[] types;

            try
            {
                types = assembly.GetTypes();
            }
            catch (ReflectionTypeLoadException ex)
            {
                types = ex.Types.Where(t => t != null).ToArray();
            }

            foreach (var type in types)
            {
                // Skip abstract classes and interfaces
                if (type.IsAbstract || type.IsInterface)
                    continue;

                // Get all interfaces implemented by this type
                var interfaces = type.GetInterfaces();

                foreach (var interfaceType in interfaces)
                {
                    // Register services that implement interfaces ending with "AppService"
                    // This avoids hard-coded references to specific plugin types
                    if (interfaceType.Name.EndsWith("AppService"))
                    {
                        services.AddTransient(interfaceType, type);
                        Console.WriteLine($"Registered service: {interfaceType.Name} -> {type.Name}");
                    }
                }
            }
        }

        /// <summary>
        /// Get Arabic tab name based on plugin name
        /// </summary>
        /// <param name="pluginName">English plugin name</param>
        /// <returns>Arabic tab name</returns>
        private string GetArabicTabName(string pluginName)
        {
            return pluginName.ToLower() switch
            {
                var name when name.Contains("notification") => LocalizedMessages.NotificationsTab,
                var name when name.Contains("print") => LocalizedMessages.PrintTab,
                var name when name.Contains("scanner") => LocalizedMessages.ScannerTab,
                var name when name.Contains("barcode") => LocalizedMessages.BarcodeTab,
                var name when name.Contains("port") => LocalizedMessages.PortTab,
                _ => pluginName // Default to original name if no match
            };
        }

        /// <summary>
        /// Save all settings from all plugin tabs
        /// </summary>
        private async void BtnSaveAllSettings_Click(object sender, EventArgs e)
        {
            try
            {
                bool allSaved = true;
                string errors = "";

                // Iterate through all tabs and save settings
                foreach (TabPage tab in tabControl1.TabPages)
                {
                    try
                    {
                        // Look for save methods in the tab's controls
                        await SaveTabSettings(tab);
                    }
                    catch (Exception ex)
                    {
                        allSaved = false;
                        errors += $"{tab.Text}: {ex.Message}\n";
                    }
                }

                if (allSaved)
                {
                    MessageBox.Show(LocalizedMessages.SettingsSaved, LocalizedMessages.Success, MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show(LocalizedMessages.SettingsSaveError(errors), LocalizedMessages.Error, MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(LocalizedMessages.SettingsSaveError(ex.Message), LocalizedMessages.Error, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Save settings for a specific tab
        /// </summary>
        private async Task SaveTabSettings(TabPage tab)
        {
            // Look for save buttons and click them, or look for save methods
            foreach (Control control in tab.Controls)
            {
                if (control is Button button && (button.Name.Contains("Save") || button.Text.Contains("حفظ") || button.Text.Contains("Save")))
                {
                    button.PerformClick();
                    await Task.Delay(100); // Small delay to allow save operation
                }
                else if (control is UserControl userControl)
                {
                    // Look for save methods in user controls
                    await SaveUserControlSettings(userControl);
                }
            }
        }

        /// <summary>
        /// Save settings for user controls
        /// </summary>
        private async Task SaveUserControlSettings(UserControl userControl)
        {
            // Use reflection to find and call save methods
            var saveMethod = userControl.GetType().GetMethod("SaveSettings") ??
                           userControl.GetType().GetMethod("Save") ??
                           userControl.GetType().GetMethod("SaveConfiguration");

            if (saveMethod != null)
            {
                var result = saveMethod.Invoke(userControl, null);
                if (result is Task task)
                {
                    await task;
                }
            }
        }
    }
}
