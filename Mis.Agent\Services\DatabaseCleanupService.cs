using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Mis.Agent.Resources;
using System;
using System.Data.SQLite;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace MIS.Agent.Services
{
    /// <summary>
    /// Background service for automatic SQLite database cleanup and table management
    /// </summary>
    public class DatabaseCleanupService : BackgroundService
    {
        private readonly ILogger<DatabaseCleanupService> _logger;
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(24); // Run daily
        private readonly string _databasePath;

        public DatabaseCleanupService(ILogger<DatabaseCleanupService> logger)
        {
            _logger = logger;
            _databasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "notifications.db");
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // Ensure database and tables exist on startup
            await EnsureDatabaseAndTables();

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await PerformCleanup();
                    await Task.Delay(_cleanupInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error during database cleanup");
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken); // Retry after 1 hour on error
                }
            }
        }

        /// <summary>
        /// Ensure database file and required tables exist
        /// </summary>
        public async Task EnsureDatabaseAndTables()
        {
            try
            {
                // Create database file if it doesn't exist
                if (!File.Exists(_databasePath))
                {
                    _logger.LogInformation("Creating database file: {DatabasePath}", _databasePath);
                    SQLiteConnection.CreateFile(_databasePath);
                }

                using var connection = new SQLiteConnection($"Data Source={_databasePath}");
                await connection.OpenAsync();

                // Create Logs table if it doesn't exist
                await CreateLogsTable(connection);

                // Create Notifications table if it doesn't exist
                await CreateNotificationsTable(connection);

                _logger.LogInformation("Database and tables verification completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error ensuring database and tables exist");
                throw;
            }
        }

        /// <summary>
        /// Create Logs table if it doesn't exist
        /// </summary>
        private async Task CreateLogsTable(SQLiteConnection connection)
        {
            var createTableQuery = @"
                CREATE TABLE IF NOT EXISTS Logs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Message TEXT NOT NULL,
                    Level TEXT DEFAULT 'INFO',
                    RequestData TEXT,
                    ResponseData TEXT,
                    BarcodeData TEXT,
                    ApiEndpoint TEXT,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            using var command = new SQLiteCommand(createTableQuery, connection);
            await command.ExecuteNonQueryAsync();
            _logger.LogInformation("Logs table verified/created");
        }

        /// <summary>
        /// Create Notifications table if it doesn't exist
        /// </summary>
        private async Task CreateNotificationsTable(SQLiteConnection connection)
        {
            var createTableQuery = @"
                CREATE TABLE IF NOT EXISTS Notifications (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Title TEXT NOT NULL,
                    Message TEXT NOT NULL,
                    Type TEXT DEFAULT 'INFO',
                    IsRead BOOLEAN DEFAULT 0,
                    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP
                )";

            using var command = new SQLiteCommand(createTableQuery, connection);
            await command.ExecuteNonQueryAsync();
            _logger.LogInformation("Notifications table verified/created");
        }

        /// <summary>
        /// Perform database cleanup operations - Delete ALL records from both tables
        /// </summary>
        public async Task PerformCleanup()
        {
            try
            {
                if (!File.Exists(_databasePath))
                {
                    _logger.LogInformation("Database file not found, skipping cleanup");
                    return;
                }

                using var connection = new SQLiteConnection($"Data Source={_databasePath}");
                await connection.OpenAsync();

                // Delete all records from Logs table
                await DeleteAllLogs(connection);

                // Delete all records from Notifications table
                await DeleteAllNotifications(connection);

                // Vacuum database to reclaim space
                await VacuumDatabase(connection);

                _logger.LogInformation("Database cleanup completed successfully - All records deleted");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during database cleanup operation");
                throw;
            }
        }

        /// <summary>
        /// Delete all records from Logs table
        /// </summary>
        private async Task DeleteAllLogs(SQLiteConnection connection)
        {
            var query = "DELETE FROM Logs";
            using var command = new SQLiteCommand(query, connection);
            var deletedRows = await command.ExecuteNonQueryAsync();
            _logger.LogInformation($"Deleted {deletedRows} log records");
        }

        /// <summary>
        /// Delete all records from Notifications table
        /// </summary>
        private async Task DeleteAllNotifications(SQLiteConnection connection)
        {
            var query = "DELETE FROM Notifications";
            using var command = new SQLiteCommand(query, connection);
            var deletedRows = await command.ExecuteNonQueryAsync();
            _logger.LogInformation($"Deleted {deletedRows} notification records");
        }



        /// <summary>
        /// Vacuum database to reclaim space after deletions
        /// </summary>
        private async Task VacuumDatabase(SQLiteConnection connection)
        {
            try
            {
                using var command = new SQLiteCommand("VACUUM", connection);
                await command.ExecuteNonQueryAsync();
                _logger.LogInformation("Database vacuum completed");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to vacuum database");
            }
        }

        /// <summary>
        /// Log API request/response or barcode data
        /// </summary>
        public async Task LogApiActivity(string message, string level = "INFO", string? requestData = null,
            string? responseData = null, string? barcodeData = null, string? apiEndpoint = null)
        {
            try
            {
                await EnsureDatabaseAndTables();

                using var connection = new SQLiteConnection($"Data Source={_databasePath}");
                await connection.OpenAsync();

                var insertQuery = @"
                    INSERT INTO Logs (Message, Level, RequestData, ResponseData, BarcodeData, ApiEndpoint, CreatedDate)
                    VALUES (@Message, @Level, @RequestData, @ResponseData, @BarcodeData, @ApiEndpoint, @CreatedDate)";

                using var command = new SQLiteCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@Message", message);
                command.Parameters.AddWithValue("@Level", level);
                command.Parameters.AddWithValue("@RequestData", requestData ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ResponseData", responseData ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@BarcodeData", barcodeData ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@ApiEndpoint", apiEndpoint ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                await command.ExecuteNonQueryAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to log API activity");
            }
        }

        /// <summary>
        /// Log barcode scan data
        /// </summary>
        public async Task LogBarcodeData(string barcodeValue, string? additionalInfo = null)
        {
            await LogApiActivity(
                message: $"Barcode scanned: {barcodeValue}",
                level: "INFO",
                barcodeData: barcodeValue,
                requestData: additionalInfo
            );
        }

        /// <summary>
        /// Log API request and response
        /// </summary>
        public async Task LogApiRequest(string endpoint, string requestData, string responseData, string level = "INFO")
        {
            await LogApiActivity(
                message: $"API call to {endpoint}",
                level: level,
                requestData: requestData,
                responseData: responseData,
                apiEndpoint: endpoint
            );
        }

        /// <summary>
        /// Manually trigger cleanup (for testing or immediate cleanup)
        /// </summary>
        public static async Task TriggerCleanup(ILogger? logger = null)
        {
            try
            {
                var typedLogger = (ILogger<DatabaseCleanupService>)(logger ?? NullLogger<DatabaseCleanupService>.Instance);
                var service = new DatabaseCleanupService(typedLogger);
                await service.PerformCleanup();
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Manual database cleanup failed");
                throw;
            }
        }

        /// <summary>
        /// Get database statistics
        /// </summary>
        public static async Task<DatabaseStats> GetDatabaseStats()
        {
            var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "notifications.db");
            if (!File.Exists(dbPath))
            {
                return new DatabaseStats { FileExists = false };
            }

            var fileInfo = new FileInfo(dbPath);
            var stats = new DatabaseStats
            {
                FileExists = true,
                FileSizeBytes = fileInfo.Length,
                LastModified = fileInfo.LastWriteTime
            };

            try
            {
                using var connection = new SQLiteConnection($"Data Source={dbPath}");
                await connection.OpenAsync();

                // Get logs record count
                using var logsCountCommand = new SQLiteCommand("SELECT COUNT(*) FROM Logs", connection);
                stats.LogsRecordCount = Convert.ToInt32(await logsCountCommand.ExecuteScalarAsync());

                // Get notifications record count
                using var notificationsCountCommand = new SQLiteCommand("SELECT COUNT(*) FROM Notifications", connection);
                stats.NotificationsRecordCount = Convert.ToInt32(await notificationsCountCommand.ExecuteScalarAsync());

                // Get oldest log record date
                using var oldestCommand = new SQLiteCommand("SELECT MIN(CreatedDate) FROM Logs", connection);
                var oldestResult = await oldestCommand.ExecuteScalarAsync();
                if (oldestResult != DBNull.Value && oldestResult != null)
                {
                    stats.OldestRecordDate = Convert.ToDateTime(oldestResult);
                }
            }
            catch (Exception ex)
            {
                stats.Error = ex.Message;
            }

            return stats;
        }
    }

    /// <summary>
    /// Database statistics information
    /// </summary>
    public class DatabaseStats
    {
        public bool FileExists { get; set; }
        public long FileSizeBytes { get; set; }
        public DateTime LastModified { get; set; }
        public int RecordCount { get; set; } // For backward compatibility
        public int LogsRecordCount { get; set; }
        public int NotificationsRecordCount { get; set; }
        public DateTime? OldestRecordDate { get; set; }
        public string? Error { get; set; }

        public string FileSizeFormatted => FileExists ? FormatBytes(FileSizeBytes) : "N/A";

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = ["B", "KB", "MB", "GB"];
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
