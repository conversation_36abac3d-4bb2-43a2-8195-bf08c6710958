using Mis.Shared.Interface;
using System.Diagnostics;

namespace Mis.Agent.Barcode
{
    public partial class BarcodeForm : Form
    {
        private readonly IBarcodeAppService _barcodeAppService;
        public BarcodeForm(IBarcodeAppService barcodeAppService)
        {
            _barcodeAppService = barcodeAppService;
            InitializeComponent();
        }
        private async void SaveBarcodeConfiguration_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate and update the URL
                string newBaseUrl = barcodeUrlTextBox.Text;
                bool result = _barcodeAppService.IsValidBarcodeUrl(newBaseUrl);

                if (string.IsNullOrEmpty(newBaseUrl) /*|| !result*/)
                {
                    _barcodeAppService.ShowNotification("Invalid URL", "Please enter a valid URL.");
                    return;
                }

                // Update the BaseUrl in appsettings.json if it's different
                var currentUrl = _barcodeAppService.GetBarcodeBaseUrl();
                if (currentUrl != newBaseUrl)
                {
                    _barcodeAppService.UpdateBarcodeBaseUrl(newBaseUrl);
                    _barcodeAppService.ShowNotification("Update Successful", "URL updated successfully. Restarting the application...");
                }

                // Get the selected COM port from the ComboBox
                string selectedComPort = comboBoxCOMPorts.SelectedItem as string;
                if (string.IsNullOrEmpty(selectedComPort))
                {
                    _barcodeAppService.ShowNotification("لم يتم اختيار منفذ", "الرجاء اختيار منفذ COM.");
                    return;
                }

                if (!ConfirmBarcodePortAvailability(selectedComPort))
                    return;

                _barcodeAppService.UpdateCOMPort(selectedComPort);

                // Display the selected COM port in textBox
                comPortTextBox.Text = selectedComPort;

                // Update port status
                UpdatePortStatus(selectedComPort);

                _barcodeAppService.ShowNotification("تم الحفظ", "تم حفظ إعدادات الباركود بنجاح. سيتم تطبيق التغييرات عند إعادة تشغيل التطبيق.");

                // Instead of restarting immediately, just save the settings
                // The application will use the new settings on next startup

            }
            catch (Exception ex)
            {
                _barcodeAppService.ShowNotification("خطأ", $"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }
        private bool ConfirmBarcodePortAvailability(string selectedComPort)
        {
            if (!_barcodeAppService.IsBarcodeScannerConnected(selectedComPort))
            {
                _barcodeAppService.ShowNotification("تحذير المنفذ", $"المنفذ {selectedComPort} غير متاح أو لا يحتوي على جهاز باركود.");
                // For now, we'll continue anyway since we can't show a dialog
                // In a real implementation, you might want to handle this differently
                return true; // or false, depending on your business logic
            }

            return true;
        }
        private void ButtonTestPort_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedComPort = comboBoxCOMPorts.SelectedItem as string;
                if (string.IsNullOrEmpty(selectedComPort))
                {
                    _barcodeAppService.ShowNotification("لم يتم اختيار منفذ", "الرجاء اختيار منفذ COM أولاً.");
                    return;
                }

                bool isConnected = _barcodeAppService.IsBarcodeScannerConnected(selectedComPort);
                UpdatePortStatus(selectedComPort, isConnected);

                if (isConnected)
                {
                    _barcodeAppService.ShowNotification("اختبار المنفذ", $"المنفذ {selectedComPort} متاح ويعمل بشكل صحيح.");
                }
                else
                {
                    _barcodeAppService.ShowNotification("اختبار المنفذ", $"المنفذ {selectedComPort} غير متاح أو لا يحتوي على جهاز باركود.");
                }
            }
            catch (Exception ex)
            {
                _barcodeAppService.ShowNotification("خطأ", $"خطأ في اختبار المنفذ: {ex.Message}");
            }
        }

        private void UpdatePortStatus(string portName, bool? isConnected = null)
        {
            if (isConnected == null)
            {
                isConnected = _barcodeAppService.IsBarcodeScannerConnected(portName);
            }

            if (isConnected.Value)
            {
                labelPortStatus.Text = $"حالة المنفذ: {portName} - متصل ✓";
                labelPortStatus.ForeColor = Color.Green;
            }
            else
            {
                labelPortStatus.Text = $"حالة المنفذ: {portName} - غير متصل ✗";
                labelPortStatus.ForeColor = Color.Red;
            }
        }

        private void RestartApplication()
        {
            try
            {
                // Close the current application
                Application.Exit();

                // Start a new instance of the application
                Process.Start(Application.ExecutablePath);
            }
            catch (Exception ex)
            {
                _barcodeAppService.ShowNotification("Error", $"Failed to restart the application: {ex.Message}");
                // Optionally log the exception
            }
        }

    }
}
